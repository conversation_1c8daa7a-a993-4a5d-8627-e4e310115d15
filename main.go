package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"github.com/charmbracelet/bubbles/spinner"
	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"

	"google.golang.org/genai"
)

// Define styles for the UI using lipgloss
var (
	appStyle    = lipgloss.NewStyle().Padding(1, 2)
	headerStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#7D56F4")). // Purple
			Bold(true).
			PaddingBottom(1)
	aiMessageStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#00ADD8")). // Cyan
			<PERSON>t(2).
			Border(lipgloss.RoundedBorder(), false, false, false, true).
			BorderForeground(lipgloss.Color("#00ADD8")).
			Width(80) // Default width, adjusted by window size
	userMessageStyle = lipgloss.NewStyle().
				Foreground(lipgloss.Color("#FF8700")). // Orange
				PaddingRight(2).
				Border(lipgloss.RoundedBorder(), false, true, false, false).
				BorderForeground(lipgloss.Color("#FF8700")).
				Align(lipgloss.Right).
				Width(80) // Default width, adjusted by window size
	inputStyle = lipgloss.NewStyle().
			Border(lipgloss.NormalBorder()).
			Padding(1, 2).
			MarginTop(1)
	errorStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#FF0000")). // Red
			Bold(true)
	spinnerStyle = lipgloss.NewStyle().Foreground(lipgloss.Color("63")) // Blue-violet
)

// geminiResponseMsg is a message type for Gemini API responses
type geminiResponseMsg struct {
	Response string
	Err      error
}

// model represents the application state for Bubble Tea
type model struct {
	textInput    textinput.Model // Input field for user messages
	spinner      spinner.Model   // Loading spinner
	chatHistory  []string        // Stores all chat messages
	geminiClient *genai.Client   // Gemini AI client
	loading      bool            // Indicates if AI is generating response
	err          error           // Stores any application errors
	lastWidth    int             // Last known terminal width
	lastHeight   int             // Last known terminal height
}

// initialModel returns an initialized Bubble Tea model
func initialModel() model {
	ti := textinput.New()
	ti.Placeholder = "Type your message..."
	ti.Focus()
	ti.CharLimit = 256
	ti.Prompt = "> " // Custom prompt

	s := spinner.New()
	s.Spinner = spinner.Dot // Simple dot spinner
	s.Style = spinnerStyle

	return model{
		textInput:   ti,
		spinner:     s,
		chatHistory: []string{},
		loading:     false,
	}
}

// Init initializes the application, including the Gemini client
func (m model) Init() tea.Cmd {
	// Retrieve API key from environment variable
	apiKey := os.Getenv("GOOGLE_API_KEY")
	if apiKey == "" {
		m.err = fmt.Errorf("GOOGLE_API_KEY environment variable not set")
		return tea.Quit // Exit if API key is missing
	}

	ctx := context.Background()
	// Create a new Gemini client using the API key
	client, err := genai.NewClient(ctx, &genai.ClientConfig{
		APIKey: apiKey,
	})
	if err != nil {
		m.err = fmt.Errorf("error creating Gemini client: %w", err)
		return tea.Quit
	}
	m.geminiClient = client

	// Add an initial greeting message from the AI
	m.chatHistory = append(m.chatHistory, aiMessageStyle.Render("AI: Hello! How can I assist you today?"))

	// Return a batch of commands: focus the text input and start the spinner animation
	return tea.Batch(m.textInput.Focus(), m.spinner.Tick)
}

// Update handles incoming messages and updates the model state
func (m model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		// Adjust UI component widths based on terminal size
		m.lastWidth = msg.Width
		m.lastHeight = msg.Height
		m.textInput.Width = msg.Width - 6 // Account for padding and borders
		inputStyle = inputStyle.Width(msg.Width - 4)
		aiMessageStyle = aiMessageStyle.Width(msg.Width - 4)
		userMessageStyle = userMessageStyle.Width(msg.Width - 4)
		return m, nil

	case tea.KeyMsg:
		switch msg.Type {
		case tea.KeyCtrlC, tea.KeyEsc:
			return m, tea.Quit // Exit on Ctrl+C or Escape
		case tea.KeyEnter:
			if m.loading {
				return m, nil // Ignore input if AI is busy
			}
			userMessage := strings.TrimSpace(m.textInput.Value())
			if userMessage == "" {
				return m, nil // Ignore empty messages
			}

			// Add user's message to chat history
			m.chatHistory = append(m.chatHistory, userMessageStyle.Render(fmt.Sprintf("You: %s", userMessage)))

			m.loading = true         // Set loading state
			m.textInput.SetValue("") // Clear the input field
			// Send message to Gemini API and start spinner
			return m, tea.Batch(m.spinner.Tick, sendToGemini(m.geminiClient, userMessage))
		}

	case spinner.TickMsg:
		if m.loading {
			// Update spinner animation while loading
			m.spinner, cmd = m.spinner.Update(msg)
			cmds = append(cmds, cmd)
		}

	case geminiResponseMsg:
		m.loading = false // AI response received, stop loading
		if msg.Err != nil {
			m.err = fmt.Errorf("gemini API error: %w", msg.Err)
			return m, tea.Quit
		}
		// Add AI's response to chat history
		m.chatHistory = append(m.chatHistory, aiMessageStyle.Render(fmt.Sprintf("AI: %s", msg.Response)))

	case error:
		m.err = msg // Handle any errors that come as a tea.Msg
		return m, tea.Quit
	}

	// Update the text input component
	m.textInput, cmd = m.textInput.Update(msg)
	cmds = append(cmds, cmd)

	return m, tea.Batch(cmds...)
}

// View renders the UI for the current model state
func (m model) View() string {
	if m.err != nil {
		return appStyle.Render(errorStyle.Render(m.err.Error())) // Display error prominently
	}

	// Calculate the maximum height for the chat history
	maxChatHeight := m.lastHeight - lipgloss.Height(headerStyle.Render("")) - lipgloss.Height(inputStyle.Render(m.textInput.View())) - 4 // Approximate padding/margin for other elements
	if maxChatHeight < 0 {
		maxChatHeight = 0 // Ensure non-negative height
	}

	// Render chat history with basic scrollback
	chatView := strings.Join(m.chatHistory, "\n")
	if lipgloss.Height(chatView) > maxChatHeight {
		// If chat history is too long, show only the most recent messages that fit
		renderedLines := strings.Split(chatView, "\n")
		startIndex := len(renderedLines) - maxChatHeight
		if startIndex < 0 {
			startIndex = 0
		}
		chatView = strings.Join(renderedLines[startIndex:], "\n")
	}

	// Render the input field
	inputView := inputStyle.Render(m.textInput.View())

	// Render loading spinner and message if AI is thinking
	loadingView := ""
	if m.loading {
		loadingView = m.spinner.View() + " AI is thinking..."
	}

	// Combine all UI elements vertically
	return appStyle.Render(
		lipgloss.JoinVertical(lipgloss.Left,
			headerStyle.Render("Go Gemini Chat CLI"),
			lipgloss.NewStyle().Height(maxChatHeight).Render(chatView), // Apply max height to chat history
			loadingView,
			inputView,
		),
	)
}

// sendToGemini sends a prompt to the Gemini API and returns the response as a tea.Msg
func sendToGemini(client *genai.Client, prompt string) tea.Cmd {
	return func() tea.Msg {
		ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second) // 60-second timeout for API call
		defer cancel()

		// Generate content using the Gemini model
		resp, err := client.Models.GenerateContent(ctx, "gemini-pro", []*genai.Content{
			{
				Parts: []*genai.Part{
					{Text: prompt},
				},
			},
		}, nil)
		if err != nil {
			return geminiResponseMsg{Err: err}
		}

		// Handle empty or invalid responses
		if resp == nil {
			return geminiResponseMsg{Response: "No response from AI."}
		}

		responseText := resp.Text()
		if responseText == "" {
			return geminiResponseMsg{Response: "No response from AI."}
		}

		return geminiResponseMsg{Response: responseText}
	}
}

func main() {
	// Create a new Bubble Tea program with the initial model and enable alt screen for better TUI experience
	p := tea.NewProgram(initialModel(), tea.WithAltScreen())
	if _, err := p.Run(); err != nil {
		log.Fatalf("Alas, there's been an error: %v", err)
	}
}
